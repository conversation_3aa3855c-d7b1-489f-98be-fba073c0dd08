# GitHub Installation Token Management

This document explains how to use the GitHub installation token management system that stores tokens in MongoDB and provides API endpoints for your Python backend.

## Overview

The system automatically:
- Stores GitHub installation tokens in MongoDB
- Handles token expiration (1 hour lifetime)
- Provides automatic regeneration when tokens expire
- Offers API endpoints for external services (like your Python backend)

## Database Model

Installation tokens are stored in the `installation_tokens` collection with:
- `installation_id`: GitHub installation ID (unique)
- `token`: The actual GitHub installation token
- `expires_at`: When the token expires
- `created_at` / `updated_at`: Timestamps

MongoDB automatically deletes expired tokens using TTL indexing.

## API Endpoints

### For Python Backend (No Authentication Required)

#### Get Installation Token
```
GET /api/github/token/{installationId}
```

**Response:**
```json
{
  "success": true,
  "installation_id": "12345",
  "token": "ghs_xxxxxxxxxxxx",
  "expires_in_hours": 1,
  "organization": {
    "name": "my-org",
    "type": "organization",
    "provider": "github"
  },
  "generated_at": "2024-01-01T12:00:00.000Z"
}
```

#### Force Regenerate Token
```
POST /api/github/token/{installationId}/regenerate
```

**Response:**
```json
{
  "success": true,
  "installation_id": "12345",
  "token": "ghs_yyyyyyyyyyyy",
  "expires_in_hours": 1,
  "organization": {
    "name": "my-org",
    "type": "organization", 
    "provider": "github"
  },
  "regenerated_at": "2024-01-01T12:00:00.000Z",
  "message": "Token successfully regenerated"
}
```

### For Authenticated Users

#### Get Installation Token (with auth)
```
GET /api/github/installation-token?installation_id=12345
```

#### Force Regenerate Token (with auth)
```
POST /api/github/installation-token
Content-Type: application/json

{
  "installation_id": "12345"
}
```

#### Clear Token (with auth)
```
DELETE /api/github/installation-token?installation_id=12345
```

## Python Backend Usage

```python
import requests

class GitHubTokenManager:
    def __init__(self, base_url):
        self.base_url = base_url
    
    def get_installation_token(self, installation_id):
        """Get current token (cached or new)"""
        response = requests.get(
            f"{self.base_url}/api/github/token/{installation_id}"
        )
        
        if response.status_code == 200:
            data = response.json()
            return data["token"]
        else:
            raise Exception(f"Failed to get token: {response.json()}")
    
    def regenerate_token(self, installation_id):
        """Force regenerate token"""
        response = requests.post(
            f"{self.base_url}/api/github/token/{installation_id}/regenerate"
        )
        
        if response.status_code == 200:
            data = response.json()
            return data["token"]
        else:
            raise Exception(f"Failed to regenerate token: {response.json()}")
    
    def get_pr_diff(self, installation_id, owner, repo, pr_number):
        """Example: Get PR diff using the installation token"""
        token = self.get_installation_token(installation_id)
        
        response = requests.get(
            f"https://api.github.com/repos/{owner}/{repo}/pulls/{pr_number}",
            headers={
                "Authorization": f"Bearer {token}",
                "Accept": "application/vnd.github.v3.diff"
            }
        )
        
        return response.text

# Usage
token_manager = GitHubTokenManager("http://localhost:3000")
diff_content = token_manager.get_pr_diff("12345", "owner", "repo", 123)
```

## Token Lifecycle

1. **First Request**: Token is generated from GitHub API and saved to MongoDB
2. **Subsequent Requests**: Token is returned from MongoDB cache
3. **Near Expiry**: When token has <10 minutes left, new token is generated
4. **Automatic Cleanup**: Expired tokens are automatically deleted by MongoDB

## Error Handling

- `400`: Missing installation_id parameter
- `404`: Installation not found or not active
- `401`: Unauthorized (for authenticated endpoints)
- `500`: Server error (GitHub API issues, database problems)

## Security Notes

- The `/api/github/token/*` endpoints have no authentication for service-to-service communication
- The `/api/github/installation-token` endpoints require user authentication
- Tokens are stored securely in MongoDB
- Only active installations can generate tokens
