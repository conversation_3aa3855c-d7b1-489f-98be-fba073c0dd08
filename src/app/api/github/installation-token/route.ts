import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/src/features/auth/lib/auth-config';
import { githubAppAuth } from '@/src/lib/github/app-auth';
import { Organization, InstallationStatus } from '@/src/lib/database/models';
import connectToDatabase from '@/src/lib/database/mongoose';

/**
 * GET /api/github/installation-token?installation_id=123
 * Get current installation token (for your Python backend)
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const installationId = searchParams.get('installation_id');

    if (!installationId) {
      return NextResponse.json(
        { error: 'installation_id parameter is required' },
        { status: 400 }
      );
    }

    // Verify the installation belongs to an active organization
    await connectToDatabase();
    const organization = await Organization.findOne({
      installation_id: installationId,
      installation_status: InstallationStatus.ACTIVE
    });

    if (!organization) {
      return NextResponse.json(
        { error: 'Installation not found or not active' },
        { status: 404 }
      );
    }

    // Get token (will return cached or generate new)
    const token = await githubAppAuth.getInstallationToken(installationId);

    return NextResponse.json({
      installation_id: installationId,
      token: token,
      expires_in_hours: 1, // GitHub tokens expire in 1 hour
      generated_at: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error getting installation token:', error);
    return NextResponse.json(
      { error: 'Failed to get installation token' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/github/installation-token
 * Force regenerate installation token
 */
export async function POST(request: NextRequest) {
  try {
    // Optional: Add authentication for this endpoint
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { installation_id } = body;

    if (!installation_id) {
      return NextResponse.json(
        { error: 'installation_id is required in request body' },
        { status: 400 }
      );
    }

    // Verify the installation belongs to the user's organizations
    await connectToDatabase();
    const organization = await Organization.findOne({
      installation_id: installation_id,
      user_id: session.user.id,
      installation_status: InstallationStatus.ACTIVE
    });

    if (!organization) {
      return NextResponse.json(
        { error: 'Installation not found or not accessible' },
        { status: 404 }
      );
    }

    // Force regenerate token
    const newToken = await githubAppAuth.regenerateInstallationToken(installation_id);

    return NextResponse.json({
      installation_id: installation_id,
      token: newToken,
      expires_in_hours: 1,
      regenerated_at: new Date().toISOString(),
      message: 'Token successfully regenerated'
    });

  } catch (error) {
    console.error('Error regenerating installation token:', error);
    return NextResponse.json(
      { error: 'Failed to regenerate installation token' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/github/installation-token?installation_id=123
 * Clear/delete installation token from database
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const installationId = searchParams.get('installation_id');

    if (!installationId) {
      return NextResponse.json(
        { error: 'installation_id parameter is required' },
        { status: 400 }
      );
    }

    // Verify the installation belongs to the user's organizations
    await connectToDatabase();
    const organization = await Organization.findOne({
      installation_id: installationId,
      user_id: session.user.id,
      installation_status: InstallationStatus.ACTIVE
    });

    if (!organization) {
      return NextResponse.json(
        { error: 'Installation not found or not accessible' },
        { status: 404 }
      );
    }

    // Clear the token from database
    await githubAppAuth.clearInstallationTokenCache(installationId);

    return NextResponse.json({
      installation_id: installationId,
      message: 'Installation token cleared successfully',
      cleared_at: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error clearing installation token:', error);
    return NextResponse.json(
      { error: 'Failed to clear installation token' },
      { status: 500 }
    );
  }
}
